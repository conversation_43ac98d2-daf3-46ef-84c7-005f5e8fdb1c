"use strict";

//  scale dependent on device screen
let scale = screen.height / screen.width;

if(scale > 1){
    document.getElementById('map').setAttribute('style','height: 1500px;');
    document.querySelector('body').style.fontSize = '32px';
}


//  map init
  var mymap = L.map('map').setView([39.198951, 20.186498], 13);
  <PERSON><PERSON>tileLayer('https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token={accessToken}', {
attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',
maxZoom: 18,
id: 'mapbox/streets-v11',
tileSize: 512,
zoomOffset: -1,
accessToken: 'pk.eyJ1IjoiZ2lhbm5pc3B4MTAiLCJhIjoiY2tzMDhic2N3MDd0bzJ2cGVvN3BuZW13ZSJ9.3WH28Cfe336rVBSV5myehQ'

}).addTo(mymap);


//  boatsiconBaseUrl + init
const LeafIcon = L.Icon.extend({
    options: {
        iconSize: [36, 36]
    }
})



//  proper icon function

function directionToIcon(dir, iconBaseUrl) {
    

    if(dir == 0){
        return new LeafIcon({iconUrl: iconBaseUrl +'stoped.png'});
    }
    
    if(dir >337.5 || dir <= 22.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'n.png'});
    }

    if(dir > 22.5 && dir <= 67.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'ne.png'});
    }

    if(dir > 67.5 && dir <= 112.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'e.png'})
    }

    if(dir > 112.5 && dir <= 157.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'se.png'});
    }

    if(dir > 157.5 && dir <= 202.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'s.png'});
    }

    if(dir > 202.5 && dir <= 247.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'sw.png'});
    }

    if(dir > 247.7 && dir <= 292.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'w.png'});
    }
    
    if(dir > 292.5 && dir <= 337.5){
        return new LeafIcon({iconUrl: iconBaseUrl +'nw.png'});
    }


}

//  boats init 
const markers = {};
(async () => {
    const data = await fetch('getBoatData',{
        method : 'GET',
        headers : {'Content-Type': 'application/json'}
    });
    const dataobj = await data.json();

    dataobj.forEach(boat => {
        if(boat.data){

            const data = boat.data;
            const date = new Date(data.time);

            const infoPopUp =   `<b>${boat.name}: ${boat.imei}</b>
                                <hr>
                                <b> time:</b>    ${date.toLocaleDateString()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}<br>
                                <b>speed:</b>   ${data.speed}
                                 
            `

            console.log(boat.data);
            markers[boat.imei] = L.marker([boat.data.lat, boat.data.long], {icon: directionToIcon(boat.data.direction, boat.iconBaseUrl)}).addTo(mymap).bindPopup(infoPopUp);
        }
    });

})();

// position interval

setInterval(async() => {
        let boats = await fetch('getBoatData',{
            method : 'GET',
            headers : {'Content-Type': 'application/json'}
        });
        boats = await boats.json();
        // console.log(Array.isArray(boats));
        for(const boat of boats){
            const data = boat.data;
            const date = new Date(data.time);

            const infoPopUp =   `<b>${boat.name}: ${boat.imei}</b>
                                 <hr>
                                 <b> time:</b>    ${date.toLocaleDateString()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}<br>
                                 <b>speed:</b>   ${data.speed}<br>
                                 
                                 
            `

            markers[boat.imei].setLatLng([data.lat, data.long])
            .bindPopup(infoPopUp)
            .setIcon(directionToIcon(boat.data.direction, boat.iconBaseUrl));
            // console.log(directionToIcon(boat.data.direction, boat.iconBaseUrl));
        }
        
}, 5000);

