import { LitElement, css, html } from 'lit'

export class MyElement extends LitElement {
  static styles = css`
    :host {
      display: block;
      font-family: Arial, sans-serif;
    }

    .nav-container {
      background: #f0f0f0;
      padding: 10px;
    }

    nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    #logo {
      height: 40px;
    }

    nav ul {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 20px;
    }

    nav a {
      text-decoration: none;
      color: #333;
      padding: 8px 16px;
      border-radius: 4px;
    }

    nav a:hover {
      background: #ddd;
    }

    #map {
      height: 500px;
      width: 100%;
      margin-top: 10px;
    }

    @media (max-height: 600px) {
      #map {
        height: 400px;
      }
    }
  `

  constructor() {
    super()
    this.mapInitialized = false
  }

  firstUpdated() {
    // Load Leaflet CSS and JS dynamically
    this.loadLeaflet().then(() => {
      this.initializeMap()
    })
  }

  async loadLeaflet() {
    // Load Leaflet CSS
    if (!document.querySelector('link[href*="leaflet"]')) {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css'
      link.integrity = 'sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=='
      link.crossOrigin = ''
      document.head.appendChild(link)
    }

    // Load Leaflet JS
    if (!window.L) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js'
        script.integrity = 'sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA=='
        script.crossOrigin = ''
        script.onload = resolve
        script.onerror = reject
        document.head.appendChild(script)
      })
    }
  }

  initializeMap() {
    if (this.mapInitialized || !window.L) return

    const mapElement = this.shadowRoot.querySelector('#map')
    if (!mapElement) return

    // Scale dependent on device screen
    let scale = screen.height / screen.width
    if (scale > 1) {
      mapElement.style.height = '1500px'
      document.querySelector('body').style.fontSize = '32px'
    }

    // Map initialization
    const mymap = window.L.map(mapElement).setView([39.198951, 20.186498], 13)

    window.L.tileLayer('https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token={accessToken}', {
      attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',
      maxZoom: 18,
      id: 'mapbox/streets-v11',
      tileSize: 512,
      zoomOffset: -1,
      accessToken: 'pk.eyJ1IjoiZ2lhbm5pc3B4MTAiLCJhIjoiY2tzMDhic2N3MDd0bzJ2cGVvN3BuZW13ZSJ9.3WH28Cfe336rVBSV5myehQ'
    }).addTo(mymap)

    this.mapInitialized = true
    this.map = mymap

    // Initialize boats after map is ready
    this.initializeBoats()
  }

  // Boats icon base URL + init
  createLeafIcon() {
    return window.L.Icon.extend({
      options: {
        iconSize: [36, 36]
      }
    })
  }

  directionToIcon(dir, iconBaseUrl) {
    const LeafIcon = this.createLeafIcon()

    if (dir == 0) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'stoped.png'})
    }

    if (dir > 337.5 || dir <= 22.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'n.png'})
    }

    if (dir > 22.5 && dir <= 67.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'ne.png'})
    }

    if (dir > 67.5 && dir <= 112.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'e.png'})
    }

    if (dir > 112.5 && dir <= 157.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'se.png'})
    }

    if (dir > 157.5 && dir <= 202.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 's.png'})
    }

    if (dir > 202.5 && dir <= 247.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'sw.png'})
    }

    if (dir > 247.7 && dir <= 292.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'w.png'})
    }

    if (dir > 292.5 && dir <= 337.5) {
      return new LeafIcon({iconUrl: iconBaseUrl + 'nw.png'})
    }
  }

  async initializeBoats() {
    if (!this.map) return

    this.markers = {}

    try {
      const data = await fetch('getBoatData', {
        method: 'GET',
        headers: {'Content-Type': 'application/json'}
      })
      const dataobj = await data.json()

      dataobj.forEach(boat => {
        if (boat.data) {
          const data = boat.data
          const date = new Date(data.time)

          const infoPopUp = `<b>${boat.name}: ${boat.imei}</b>
                            <hr>
                            <b> time:</b>    ${date.toLocaleDateString()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}<br>
                            <b>speed:</b>   ${data.speed}`

          console.log(boat.data)
          this.markers[boat.imei] = window.L.marker([boat.data.lat, boat.data.long], {
            icon: this.directionToIcon(boat.data.direction, boat.iconBaseUrl)
          }).addTo(this.map).bindPopup(infoPopUp)
        }
      })

      // Start position update interval
      this.startPositionUpdates()
    } catch (error) {
      console.error('Error loading boat data:', error)
    }
  }

  startPositionUpdates() {
    setInterval(async () => {
      try {
        let boats = await fetch('getBoatData', {
          method: 'GET',
          headers: {'Content-Type': 'application/json'}
        })
        boats = await boats.json()

        for (const boat of boats) {
          if (boat.data && this.markers[boat.imei]) {
            const data = boat.data
            const date = new Date(data.time)

            const infoPopUp = `<b>${boat.name}: ${boat.imei}</b>
                              <hr>
                              <b> time:</b>    ${date.toLocaleDateString()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}<br>
                              <b>speed:</b>   ${data.speed}<br>`

            this.markers[boat.imei].setLatLng([data.lat, data.long])
              .bindPopup(infoPopUp)
              .setIcon(this.directionToIcon(boat.data.direction, boat.iconBaseUrl))
          }
        }
      } catch (error) {
        console.error('Error updating boat positions:', error)
      }
    }, 5000)
  }

  render() {
    return html`
      <div class="nav-container">
        <nav>
          <img id="logo" src="public/waterlogo.png" alt="logo">
          <ul>
            <li><a href="/">map</a></li>
            <li><a href="routerepeat">route repeat</a></li>
            <li><a href="addGuest">Create link</a></li>
            <li><a href="addBoat">Add boat</a></li>
          </ul>
        </nav>
      </div>
      <div id="map"></div>
    `
  }
}

customElements.define('my-element', MyElement)