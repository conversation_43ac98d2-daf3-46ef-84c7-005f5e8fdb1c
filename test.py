import os


# You can set your API key via env var
# e.g. in .env: GOOGLE_API_KEY=your_key
# Alternatively, you can set it manually:
GOOGLE_API_KEY= 'AIzaSyCnM2URS-tX0BFPh8s1rDilELlRCIDytmQ'
from langchain_google_genai import ChatGoogleGenerativeAI

# Create the LangChain-compatible LLM using a Gemini model
llm = ChatGoogleGenerativeAI(model="gemini-pro", temperature=0.7, )

# Use it directly:
resp = llm.invoke("Write a short poem about autumn in Athens.")
print("Response:", resp.content)
