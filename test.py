import os
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>

# Get API key from environment variable
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')

if not GOOGLE_API_KEY:
    raise ValueError("GOOGLE_API_KEY environment variable is not set. Please set it before running the script.")

# Create the LangChain-compatible LLM using a Gemini model
llm = ChatGoogleGenerativeAI(model="gemini-pro", temperature=0.7, google_api_key=GOOGLE_API_KEY)

# Use it directly:
resp = llm.invoke("Write a short poem about autumn in Athens.")
print("Response:", resp.content)
